2025-05-29 20:32:53,531 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 - HV000001: Hibernate Validator 6.0.13.Final
2025-05-29 20:32:53,540 [restartedMain] INFO  cn.guliandigital.HuangHeApplication 55 - Starting HuangHeApplication on ZHANGYAXI-PC with PID 22924 (C:\Users\<USER>\1024\code\gulian-huanghe-parent\huanghe-admin-parent\huanghe-admin\target\classes started by zhangyaxi in C:\Users\<USER>\1024\code\gulian-huanghe-parent\huanghe-admin-parent)
2025-05-29 20:32:53,541 [restartedMain] DEBUG cn.guliandigital.HuangHeApplication 56 - Running with Spring Boot v2.2.13.RELEASE, Spring v5.2.12.RELEASE
2025-05-29 20:32:53,547 [restartedMain] INFO  cn.guliandigital.HuangHeApplication 655 - The following profiles are active: dev
2025-05-29 20:32:56,686 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol 173 - Initializing ProtocolHandler ["http-nio-10077"]
2025-05-29 20:32:56,687 [restartedMain] INFO  org.apache.catalina.core.StandardService 173 - Starting service [Tomcat]
2025-05-29 20:32:56,687 [restartedMain] INFO  org.apache.catalina.core.StandardEngine 173 - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-05-29 20:32:56,764 [restartedMain] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/huanghe] 173 - Initializing Spring embedded WebApplicationContext
2025-05-29 20:32:58,143 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.homePage.mapper.TUserLoginSumMapper
2025-05-29 20:32:58,240 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.homePage.mapper.TUserVisitSumMapper
2025-05-29 20:32:58,254 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.nameAssociation.mapper.TProNameAssociationMapper
2025-05-29 20:32:58,267 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.order.mapper.TProOrderMapper
2025-05-29 20:32:58,282 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.plat.mapper.TPlatOrganIpMapper
2025-05-29 20:32:58,293 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.plat.mapper.TPlatOrganMapper
2025-05-29 20:32:58,306 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.plat.mapper.TPlatOrganTypeMapper
2025-05-29 20:32:58,315 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.plat.mapper.TPlatUserMapper
2025-05-29 20:32:58,330 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.book.mapper.TProBooksMapper
2025-05-29 20:32:58,354 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.clasic.mapper.TConfigClassicMapper
2025-05-29 20:32:58,363 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.clasic.mapper.TConfigClassicTreeMapper
2025-05-29 20:32:58,378 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.database.mapper.TProDatabaseMapper
2025-05-29 20:32:58,389 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.menu.mapper.TProBookMenuMapper
2025-05-29 20:32:58,411 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.readhistory.mapper.TUserReadHistoryMapper
2025-05-29 20:32:58,423 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.readhistory.mapper.TUserReadHistorySumMapper
2025-05-29 20:32:58,435 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.readnotes.mapper.TUserReadNotesMapper
2025-05-29 20:32:58,444 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.search.mapper.TUserSearchHistoryMapper
2025-05-29 20:32:58,461 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.product.userbook.mapper.TUserBookshelfMapper
2025-05-29 20:32:58,630 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.tsystem.mapper.TSysBannerMapper
2025-05-29 20:32:58,639 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.tsystem.mapper.TSysFeedbackMapper
2025-05-29 20:32:58,647 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.tsystem.mapper.TSysLinkMapper
2025-05-29 20:32:58,655 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.tsystem.mapper.TSysNewsMapper
2025-05-29 20:32:58,662 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.tsystem.mapper.TSysProjectMapper
2025-05-29 20:32:58,669 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.pvlog.mapper.TUserVisitLogMapper
2025-05-29 20:32:58,680 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.tsystem.mapper.TWxampNoticeMapper
2025-05-29 20:32:58,688 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.utils.mapper.ShujuCalendarconversionMapper
2025-05-29 20:32:58,698 [restartedMain] INFO  cn.guliandigital.common.core.redis.MybatisRedisCache 38 - Redis Cache id cn.guliandigital.utils.mapper.TUtilDictMapper
2025-05-29 20:32:58,828 [restartedMain] INFO  cn.guliandigital.framework.security.filter.SessionTextCopySecurityFilter 63 - ==>初始化配置参数 [minuteMaxCount][showCodeUrl][minuteLimitCount]
2025-05-29 20:32:58,841 [restartedMain] INFO  cn.guliandigital.framework.security.filter.RefreshTokenSecurityFilter 55 - ==>初始化配置参数 [用户token刷新]
2025-05-29 20:32:58,928 [restartedMain] INFO  cn.guliandigital.framework.security.filter.RefreshTokenSecurityFilter 55 - ==>初始化配置参数 [用户token刷新]
2025-05-29 20:32:58,930 [restartedMain] INFO  cn.guliandigital.framework.security.filter.SessionTextCopySecurityFilter 63 - ==>初始化配置参数 [minuteMaxCount][showCodeUrl][minuteLimitCount]
2025-05-29 20:32:59,038 [restartedMain] DEBUG cn.guliandigital.framework.security.filter.JwtAuthenticationTokenFilter 242 - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-05-29 20:32:59,038 [restartedMain] INFO  cn.guliandigital.framework.security.filter.SessionTextCopySecurityFilter 63 - ==>初始化配置参数 [minuteMaxCount][showCodeUrl][minuteLimitCount]
2025-05-29 20:32:59,038 [restartedMain] DEBUG cn.guliandigital.framework.security.filter.SessionTextCopySecurityFilter 242 - Filter 'sessionTextCopySecurityFilter' configured for use
2025-05-29 20:32:59,049 [restartedMain] DEBUG cn.guliandigital.framework.security.filter.AddResponseHeaderFilter 242 - Filter 'addResponseHeaderFilter' configured for use
2025-05-29 20:32:59,050 [restartedMain] INFO  cn.guliandigital.framework.security.filter.RefreshTokenSecurityFilter 55 - ==>初始化配置参数 [用户token刷新]
2025-05-29 20:32:59,050 [restartedMain] DEBUG cn.guliandigital.framework.security.filter.RefreshTokenSecurityFilter 242 - Filter 'refreshTokenSecurityFilter' configured for use
2025-05-29 20:32:59,118 [restartedMain] INFO  cn.guliandigital.common.config.HuangHeConfig 205 - ==>platHttpUrl是：/wapi/common/download?filename=
2025-05-29 20:32:59,118 [restartedMain] INFO  cn.guliandigital.common.config.HuangHeConfig 162 - ==>上传路径是：./huanghe/uploadPath
2025-05-29 20:32:59,429 [restartedMain] INFO  org.mongodb.driver.cluster 71 - Cluster created with settings {hosts=[*************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='3600000 ms', maxWaitQueueSize=500}
2025-05-29 20:32:59,635 [cluster-ClusterId{value='683853fb5531c512d3423abe', description='null'}-*************:27017] INFO  org.mongodb.driver.connection 71 - Opened connection [connectionId{localValue:1, serverValue:213}] to *************:27017
2025-05-29 20:32:59,660 [cluster-ClusterId{value='683853fb5531c512d3423abe', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster 71 - Monitor thread successfully connected to server with description ServerDescription{address=*************:27017, type=STANDALONE, state=CONNECTED, ok=true, version=ServerVersion{versionList=[3, 6, 6]}, minWireVersion=0, maxWireVersion=6, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=21879800}
2025-05-29 20:33:00,129 [restartedMain] INFO  cn.guliandigital.framework.config.ElasticsearchConfig 70 - ==>证书地址：D:\siming-admin-parent\elastic-certificates.p12
2025-05-29 20:33:00,772 [restartedMain] INFO  org.elasticsearch.plugins.PluginsService 197 - no modules loaded
2025-05-29 20:33:00,774 [restartedMain] INFO  org.elasticsearch.plugins.PluginsService 200 - loaded plugin [org.elasticsearch.index.reindex.ReindexPlugin]
2025-05-29 20:33:00,774 [restartedMain] INFO  org.elasticsearch.plugins.PluginsService 200 - loaded plugin [org.elasticsearch.join.ParentJoinPlugin]
2025-05-29 20:33:00,774 [restartedMain] INFO  org.elasticsearch.plugins.PluginsService 200 - loaded plugin [org.elasticsearch.percolator.PercolatorPlugin]
2025-05-29 20:33:00,775 [restartedMain] INFO  org.elasticsearch.plugins.PluginsService 200 - loaded plugin [org.elasticsearch.script.mustache.MustachePlugin]
2025-05-29 20:33:00,775 [restartedMain] INFO  org.elasticsearch.plugins.PluginsService 200 - loaded plugin [org.elasticsearch.transport.Netty4Plugin]
2025-05-29 20:33:00,775 [restartedMain] INFO  org.elasticsearch.plugins.PluginsService 200 - loaded plugin [org.elasticsearch.xpack.core.XPackClientPlugin]
2025-05-29 20:33:08,060 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource 991 - {dataSource-1} inited
2025-05-29 20:33:08,083 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysConfigMapper.selectConfigList 159 - ==>  Preparing: select config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark from sys_config 
2025-05-29 20:33:08,484 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysConfigMapper.selectConfigList 159 - ==> Parameters: 
2025-05-29 20:33:08,616 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysConfigMapper.selectConfigList 159 - <==      Total: 4
2025-05-29 20:33:09,098 [restartedMain] INFO  io.lettuce.core.EpollProvider 105 - Starting without optional epoll library
2025-05-29 20:33:09,104 [restartedMain] INFO  io.lettuce.core.KqueueProvider 105 - Starting without optional kqueue library
2025-05-29 20:33:09,422 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictTypeMapper.selectDictTypeAll 159 - ==>  Preparing: select dict_id, dict_name, dict_type, status, create_by, create_time, remark from sys_dict_type 
2025-05-29 20:33:09,423 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictTypeMapper.selectDictTypeAll 159 - ==> Parameters: 
2025-05-29 20:33:09,447 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictTypeMapper.selectDictTypeAll 159 - <==      Total: 24
2025-05-29 20:33:09,448 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,495 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_user_sex(String)
2025-05-29 20:33:09,512 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 3
2025-05-29 20:33:09,578 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,579 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_show_hide(String)
2025-05-29 20:33:09,596 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,614 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,615 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_normal_disable(String)
2025-05-29 20:33:09,632 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,649 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,649 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_job_status(String)
2025-05-29 20:33:09,666 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,684 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,684 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_job_group(String)
2025-05-29 20:33:09,701 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,718 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,719 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_yes_no(String)
2025-05-29 20:33:09,736 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,753 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,754 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_notice_type(String)
2025-05-29 20:33:09,772 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,790 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,791 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_notice_status(String)
2025-05-29 20:33:09,809 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,827 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,828 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_oper_type(String)
2025-05-29 20:33:09,846 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 9
2025-05-29 20:33:09,864 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,864 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_common_status(String)
2025-05-29 20:33:09,883 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,900 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,901 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: handle_status(String)
2025-05-29 20:33:09,918 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,933 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,934 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: data_from(String)
2025-05-29 20:33:09,952 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:09,969 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:09,970 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: publish_status(String)
2025-05-29 20:33:09,986 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 3
2025-05-29 20:33:10,005 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,006 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: order_status(String)
2025-05-29 20:33:10,024 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:10,042 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,043 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: auth_method(String)
2025-05-29 20:33:10,059 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:10,109 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,110 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_project_type(String)
2025-05-29 20:33:10,128 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 11
2025-05-29 20:33:10,145 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,146 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_project_status(String)
2025-05-29 20:33:10,162 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 3
2025-05-29 20:33:10,178 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,178 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: database_status(String)
2025-05-29 20:33:10,196 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:10,213 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,213 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: order_type(String)
2025-05-29 20:33:10,229 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:10,246 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,247 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: in_status(String)
2025-05-29 20:33:10,264 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 3
2025-05-29 20:33:10,281 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,282 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: data_status(String)
2025-05-29 20:33:10,301 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 5
2025-05-29 20:33:10,317 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,317 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: wxamp_notice(String)
2025-05-29 20:33:10,333 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 2
2025-05-29 20:33:10,350 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,351 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: res_type_1(String)
2025-05-29 20:33:10,367 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 3
2025-05-29 20:33:10,383 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data where status = '0' and dict_type = ? order by dict_sort asc 
2025-05-29 20:33:10,384 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - ==> Parameters: sys_banner_group(String)
2025-05-29 20:33:10,401 [restartedMain] DEBUG cn.guliandigital.system.mapper.SysDictDataMapper.selectDictDataByType 159 - <==      Total: 4
2025-05-29 20:33:12,214 [restartedMain] INFO  springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping 69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-05-29 20:33:14,320 [restartedMain] ERROR org.springframework.data.elasticsearch.repository.support.AbstractElasticsearchRepository 93 - failed to load elasticsearch nodes : org.elasticsearch.client.transport.NoNodeAvailableException: None of the configured nodes are available: [{#transport#-1}{KR6ITRcgTTKMSi1XNGX17w}{192.168.10.152}{192.168.10.152:9300}]
2025-05-29 20:33:14,381 [restartedMain] ERROR org.springframework.data.elasticsearch.repository.support.AbstractElasticsearchRepository 93 - failed to load elasticsearch nodes : org.elasticsearch.client.transport.NoNodeAvailableException: None of the configured nodes are available: [{#transport#-1}{KR6ITRcgTTKMSi1XNGX17w}{192.168.10.152}{192.168.10.152:9300}]
2025-05-29 20:33:15,692 [restartedMain] INFO  springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper 160 - Context refreshed
2025-05-29 20:33:15,815 [restartedMain] INFO  springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper 163 - Found 1 custom documentation plugin(s)
2025-05-29 20:33:16,047 [restartedMain] INFO  springfox.documentation.spring.web.scanners.ApiListingReferenceScanner 41 - Scanning for api listing references
2025-05-29 20:33:16,555 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol 173 - Starting ProtocolHandler ["http-nio-10077"]
2025-05-29 20:33:16,587 [restartedMain] INFO  cn.guliandigital.HuangHeApplication 61 - Started HuangHeApplication in 23.569 seconds (JVM running for 24.555)
2025-05-29 20:34:12,138 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog 110 - Reconnecting, last destination was /**************:6379
2025-05-29 20:34:12,166 [lettuce-nioEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler 110 - Reconnected to **************:6379
2025-05-29 20:35:13,315 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog 110 - Reconnecting, last destination was /**************:6379
2025-05-29 20:35:13,336 [lettuce-nioEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler 110 - Reconnected to **************:6379
2025-05-29 20:35:43,666 [http-nio-10077-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/huanghe] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 20:35:51,566 [http-nio-10077-exec-2] DEBUG cn.guliandigital.system.mapper.SysUserMapper.selectUserByUserName 159 - ==>  Preparing: select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,u.name_display, r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status,u.update_password_time from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where u.user_name = ? and u.del_flag='0' 
2025-05-29 20:35:51,612 [http-nio-10077-exec-2] DEBUG cn.guliandigital.system.mapper.SysUserMapper.selectUserByUserName 159 - ==> Parameters: admin(String)
2025-05-29 20:35:51,646 [http-nio-10077-exec-2] DEBUG cn.guliandigital.system.mapper.SysUserMapper.selectUserByUserName 159 - <==      Total: 1
2025-05-29 20:35:51,777 [http-nio-10077-exec-2] INFO  cn.guliandigital.framework.web.service.UserDetailsServiceImpl 80 - ==>登录用户获取的操作系统：Windows 10
2025-05-29 20:35:51,967 [schedule-pool-1] INFO  cn.guliandigital.framework.manager.factory.AsyncFactory 61 - [127.0.0.1]本机地址	本机地址		[admin][Success][登录成功]
2025-05-29 20:35:51,969 [schedule-pool-1] DEBUG cn.guliandigital.system.mapper.SysLogininforMapper.insertLogininfor 159 - ==>  Preparing: insert into sys_logininfor (user_name, status, ipaddr, login_location, browser, os, msg, login_time) values (?, ?, ?, ?, ?, ?, ?, sysdate()) 
2025-05-29 20:35:52,072 [schedule-pool-1] DEBUG cn.guliandigital.system.mapper.SysLogininforMapper.insertLogininfor 159 - ==> Parameters: admin(String), 0(String), 127.0.0.1(String), 本机地址	本机地址		(String), Chrome 13(String), Windows 10(String), 登录成功(String)
2025-05-29 20:35:52,256 [schedule-pool-1] DEBUG cn.guliandigital.system.mapper.SysLogininforMapper.insertLogininfor 159 - <==    Updates: 1
2025-05-29 20:35:53,473 [http-nio-10077-exec-5] DEBUG cn.guliandigital.system.mapper.SysMenuMapper.selectMenuTreeAll 159 - ==>  Preparing: select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.menu_type, m.icon, m.order_num, m.create_time from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 order by m.parent_id, m.order_num 
2025-05-29 20:35:53,500 [http-nio-10077-exec-5] DEBUG cn.guliandigital.system.mapper.SysMenuMapper.selectMenuTreeAll 159 - ==> Parameters: 
2025-05-29 20:35:53,530 [http-nio-10077-exec-5] DEBUG cn.guliandigital.system.mapper.SysMenuMapper.selectMenuTreeAll 159 - <==      Total: 31
2025-05-29 20:35:59,596 [http-nio-10077-exec-18] DEBUG cn.guliandigital.tsystem.mapper.TSysBannerMapper.selectTSysBannerList_COUNT 159 - ==>  Preparing: SELECT count(0) FROM t_sys_banner WHERE del_flag = ? 
2025-05-29 20:35:59,618 [http-nio-10077-exec-18] DEBUG cn.guliandigital.tsystem.mapper.TSysBannerMapper.selectTSysBannerList_COUNT 159 - ==> Parameters: 0(Integer)
2025-05-29 20:35:59,636 [http-nio-10077-exec-18] DEBUG cn.guliandigital.tsystem.mapper.TSysBannerMapper.selectTSysBannerList_COUNT 159 - <==      Total: 1
2025-05-29 20:36:06,700 [http-nio-10077-exec-28] INFO  cn.guliandigital.framework.aspectj.WebLogAspect 136 - 
=========访问开始=====
请求方法		：class cn.guliandigital.web.controller.tsystem.TSysBannerController.upload()
请求url		：http://127.0.0.1:10077/huanghe/tsystem/banner/upload
请求IP		：127.0.0.1
请求方式		：POST
请求KEY		：[file]
请求VALUE	：[org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@1ca31f87]

2025-05-29 20:36:06,752 [schedule-pool-1] DEBUG cn.guliandigital.system.mapper.SysOperLogMapper.insertOperlog 159 - ==>  Preparing: insert into sys_oper_log(title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate()) 
2025-05-29 20:36:06,761 [http-nio-10077-exec-28] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver 199 - Resolved [java.lang.RuntimeException: java.io.IOException: java.io.FileNotFoundException: C:\Users\<USER>\1024\code\gulian-huanghe-parent\huanghe-admin-parent\huanghe-admin\work\Tomcat\localhost\huanghe\.\huanghe\uploadPath\upload\banner\2025\05\29\4e58b189-112c-4b9d-91ef-c9515b591523.mp4 (系统找不到指定的路径。)]
2025-05-29 20:36:06,768 [schedule-pool-1] DEBUG cn.guliandigital.system.mapper.SysOperLogMapper.insertOperlog 159 - ==> Parameters: 轮播图文件上传(String), 2(Integer), cn.guliandigital.web.controller.tsystem.TSysBannerController.upload()(String), POST(String), 1(Integer), admin(String), null, /huanghe/tsystem/banner/upload(String), 127.0.0.1(String), 本机地址	本机地址		(String), (String), null(String), 1(Integer), java.io.IOException: java.io.FileNotFoundException: C:\Users\<USER>\1024\code\gulian-huanghe-parent\huanghe-admin-parent\huanghe-admin\work\Tomcat\localhost\huanghe\.\huanghe\uploadPath\upload\banner\2025\05\29\4e58b189-112c-4b9d-91ef-c9515b591523.mp4 (系统找不到指定的路径。)(String)
2025-05-29 20:36:06,813 [schedule-pool-1] DEBUG cn.guliandigital.system.mapper.SysOperLogMapper.insertOperlog 159 - <==    Updates: 1
2025-05-29 20:36:47,617 [http-nio-10077-exec-29] INFO  cn.guliandigital.framework.aspectj.WebLogAspect 136 - 
=========访问开始=====
请求方法		：class cn.guliandigital.web.controller.tsystem.TSysBannerController.upload()
请求url		：http://127.0.0.1:10077/huanghe/tsystem/banner/upload
请求IP		：127.0.0.1
请求方式		：POST
请求KEY		：[file]
请求VALUE	：[org.springframework.web.multipart.support.StandardMultipartHttpServletRequest$StandardMultipartFile@7d91fd0a]

2025-05-29 20:36:47,638 [http-nio-10077-exec-29] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver 199 - Resolved [java.lang.RuntimeException: java.io.IOException: java.io.FileNotFoundException: C:\Users\<USER>\1024\code\gulian-huanghe-parent\huanghe-admin-parent\huanghe-admin\work\Tomcat\localhost\huanghe\.\huanghe\uploadPath\upload\banner\2025\05\29\27a1e2f0-19a3-4458-88d9-1f6b12f94888.mp4 (系统找不到指定的路径。)]
2025-05-29 20:36:47,650 [schedule-pool-2] DEBUG cn.guliandigital.system.mapper.SysOperLogMapper.insertOperlog 159 - ==>  Preparing: insert into sys_oper_log(title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, sysdate()) 
2025-05-29 20:36:47,652 [schedule-pool-2] DEBUG cn.guliandigital.system.mapper.SysOperLogMapper.insertOperlog 159 - ==> Parameters: 轮播图文件上传(String), 2(Integer), cn.guliandigital.web.controller.tsystem.TSysBannerController.upload()(String), POST(String), 1(Integer), admin(String), null, /huanghe/tsystem/banner/upload(String), 127.0.0.1(String), 本机地址	本机地址		(String), (String), null(String), 1(Integer), java.io.IOException: java.io.FileNotFoundException: C:\Users\<USER>\1024\code\gulian-huanghe-parent\huanghe-admin-parent\huanghe-admin\work\Tomcat\localhost\huanghe\.\huanghe\uploadPath\upload\banner\2025\05\29\27a1e2f0-19a3-4458-88d9-1f6b12f94888.mp4 (系统找不到指定的路径。)(String)
2025-05-29 20:36:47,686 [schedule-pool-2] DEBUG cn.guliandigital.system.mapper.SysOperLogMapper.insertOperlog 159 - <==    Updates: 1
2025-05-29 20:37:49,011 [lettuce-eventExecutorLoop-1-13] INFO  io.lettuce.core.protocol.ConnectionWatchdog 110 - Reconnecting, last destination was /**************:6379
2025-05-29 20:37:49,030 [lettuce-nioEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler 110 - Reconnected to **************:6379
2025-05-29 20:38:18,149 [SpringContextShutdownHook] INFO  sys-user 31 - ====关闭后台任务任务线程池====
2025-05-29 20:38:18,185 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 1928 - {dataSource-1} closed
