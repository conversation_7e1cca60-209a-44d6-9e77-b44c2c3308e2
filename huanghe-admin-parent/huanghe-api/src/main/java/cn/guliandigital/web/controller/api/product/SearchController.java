package cn.guliandigital.web.controller.api.product;

import cn.guliandigital.common.annotation.PvLog;
import cn.guliandigital.common.constant.Constants;
import cn.guliandigital.common.constant.EsConstants;
import cn.guliandigital.common.constant.HttpStatus;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.domain.JsonRespResult;
import cn.guliandigital.common.enums.ApiBusinessType;
import cn.guliandigital.common.enums.DelFlagEnum;
import cn.guliandigital.common.exception.CustomException;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.ip.IpUtils;
import cn.guliandigital.common.utils.pdf.PdfUtil;
import cn.guliandigital.common.utils.pinyin.PingYinUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.es.domain.*;
import cn.guliandigital.es.service.IEsSearchService;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.plat.service.ITPlatUserService;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import cn.guliandigital.product.search.domain.TUserSearchHistory;
import cn.guliandigital.product.search.service.ITUserSearchHistoryService;
import cn.guliandigital.product.userbook.domain.TUserBookshelf;
import cn.guliandigital.product.userbook.service.ITUserBookshelfService;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @description：检索
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "首页->检索相关接口")
@Transactional
@RequestMapping("/search")
public class SearchController extends BaseController {


	@Autowired
	private SecurityPlatuserUtils securityPlatuserUtils;


	@Autowired
	private ServerConfig serverConfig;

	@Autowired
	private ITUserBookshelfService tUserBookshelfService;

	@Autowired
	private ITUserSearchHistoryService searchHistoryService;
	
	@Value("${huanghe.downloadPath}")
	private String downloadPath;

	@Autowired
	private ITProBooksService proBooksService;

	@Autowired
	private ITConfigClassicTreeService classicTreeService;

	@Autowired
	private IEsSearchService searchService;

	/**
	 * 快速检索
	 *
	 * @return
	 */
	@PvLog(title="快速检索", businessType=ApiBusinessType.SEARCH)
	@ApiOperation(value = "库内检索",notes="接口应答字段参见Swagger Models->全文数据库检索结果应答实体类")
	@RequestMapping(value = "/generalsearch", method = RequestMethod.POST)
	@ResponseBody
	public JsonRespResult<SearchResult> generalsearch(@RequestBody SearchVo searchVo, HttpServletRequest request) {
		JsonRespResult<SearchResult> respResult=new JsonRespResult<>();
		SearchResult searchResult=new SearchResult();
		try {
			List<SearchDto> list = Lists.newArrayList();
			TPlatUser user = null;
			try {
				user = securityPlatuserUtils.getUser(request);
			}catch(CustomException ignored) {}
			if (StringUtils.isEmpty(searchVo.getKeyword())){
				return respResult.error("请输入检索词");
			}else {
				//校验字符串中是否有拼音
				boolean pinyin = PingYinUtil.isPinyin(searchVo.getKeyword());
				if (pinyin){
					return respResult.error(Constants.PINYIN_TEXT);
				}
			}
			//分页
			Pageable pageable = PageRequest.of(
					searchVo.getPageNum() == 0 ? searchVo.getPageNum() : searchVo.getPageNum() - 1,
					searchVo.getPageSize());
			Page<?> page = searchService.findGeneralSearchList(searchVo, pageable);
			if (page == null) {
				return respResult.success(searchResult);
			}
			List<SearchDto> datalist = (List<SearchDto>) page.getContent();
			for (SearchDto es : datalist) {
				if(searchVo.getResultType().equals(2)){
					TProBooks book = proBooksService.selectTProBooksById(es.getBookId());
					if (StringUtil.isNotNull(book)){
						if (StringUtil.isNotEmpty(book.getCoverUrl())){
							//拼接图片路径
							String coverUrl = serverConfig.getUrl() + downloadPath + book.getCoverUrl();
							es.setCoverUrl(coverUrl);
						}
					}
					//图书结果查询收藏
					es.setHaveAddshelf(false);
					if(user != null) {
						TUserBookshelf query = new TUserBookshelf();
						query.setBookId(es.getBookId());
						query.setCreatebyId(user.getId());
						List<TUserBookshelf> bookshelves = tUserBookshelfService.selectTUserBookshelfList(query);
						if(StringUtil.isNotEmpty(bookshelves)) {
							TUserBookshelf shelf = bookshelves.get(0);
							String shelfClassId = shelf.getClassesId();
							es.setHaveAddshelf(true);
							es.setShelfClassId(shelfClassId);
						}
					}
				}
				list.add(es);
			}
			searchResult.setSearchList(list);
			respResult.setTotal(page.getTotalElements());
			return respResult.success(searchResult);
		} catch (Exception e) {
			log.error("获取书目信息出现异常！", e);
		}
		return null;
	}

	@ApiOperation(value = "高级检索-获取检索字段",notes = "该接口返回各个选项的检索字段")
	@GetMapping("/advancedsearch/getQueryFields")
	@ResponseBody
	public AjaxResult getQueryFields() {
		//书名、章节名、主编、简介、出版社、全文
		LinkedHashMap<String,String> fieldMap = Maps.newLinkedHashMap();
		fieldMap.put("bookName","书名");
		fieldMap.put("menuName","章节名");
		fieldMap.put("mainResponsibility","主编");
		fieldMap.put("bookDesc","简介");
		fieldMap.put("publisher","出版社");
		fieldMap.put("fullText","全文");
		return AjaxResult.success(fieldMap);
	}

	@PvLog(title="高级检索", businessType=ApiBusinessType.SEARCH)
	@ApiOperation(value = "高级检索",notes="接口应答字段参见Swagger Models->全文数据库检索结果应答实体类")
	@RequestMapping(value = "/advancedsearch", method = RequestMethod.POST)
	@ResponseBody
	public JsonRespResult<SearchResult> advancedsearch(@RequestBody AdvanceSearchVo searchVo, HttpServletRequest request) {
		JsonRespResult<SearchResult> respResult=new JsonRespResult<>();
		SearchResult searchResult=new SearchResult();
		try {
			List<SearchDto> list = Lists.newArrayList();
			/** ---获取用户信息 start -- **/
			TPlatUser user = null;
			try {
				user = securityPlatuserUtils.getUser(request);
			}catch(CustomException ignored) {}
			List<TSearchVo> searchList = searchVo.getSearchList();
			if (StringUtil.isEmpty(searchList)) {
				return respResult.error("请输入检索词");
			}else {
				//判断是否有拼音
				String keys = searchList.stream().filter(f->StringUtil.equalsAny(f.getQueryField(),"mainContent","bookName","author"))
						.map(TSearchVo::getKeyword).collect(Collectors.joining(""));
				boolean pinyin = PingYinUtil.isPinyin(keys);
				if (pinyin){
					return respResult.error(Constants.PINYIN_TEXT);
				}
			}
			Integer resultType = searchVo.getResultType();
			Pageable pageable = PageRequest.of(
					searchVo.getPageNum() == 0 ? searchVo.getPageNum() : searchVo.getPageNum() - 1,
					searchVo.getPageSize());
			Page<?> page = searchService.findAdvanceSearchList(searchVo, pageable);
			if (page == null) {
				return respResult.success(searchResult);
			}
			List<SearchDto> datalist = (List<SearchDto>) page.getContent();
			for (SearchDto es : datalist) {
				if(searchVo.getResultType().equals(2)){
					TProBooks book = proBooksService.selectTProBooksById(es.getBookId());
					if (StringUtil.isNotNull(book)){
						if (StringUtil.isNotEmpty(book.getCoverUrl())){
							//拼接图片路径
							String coverUrl = serverConfig.getUrl() + downloadPath + book.getCoverUrl();
							es.setCoverUrl(coverUrl);
						}
					}
					//图书结果查询收藏
					es.setHaveAddshelf(false);
					if(user != null) {
						TUserBookshelf query = new TUserBookshelf();
						query.setBookId(es.getBookId());
						query.setCreatebyId(user.getId());
						List<TUserBookshelf> bookshelves = tUserBookshelfService.selectTUserBookshelfList(query);
						if(StringUtil.isNotEmpty(bookshelves)) {
							TUserBookshelf shelf = bookshelves.get(0);
							String shelfClassId = shelf.getClassesId();
							es.setHaveAddshelf(true);
							es.setShelfClassId(shelfClassId);
						}
					}
				}
				list.add(es);
			}
			searchResult.setSearchList(list);
			respResult.setTotal(page.getTotalElements());
			return respResult.success(searchResult);
		} catch (Exception e) {
			log.error("获取高级检索数据出现异常！", e);
		}
		return null;
	}


	@ApiOperation(value = "左侧分类导航-库内检索",notes="接口应答字段参见Swagger Models->左侧分类导航结果应答实体类")
	@PostMapping(value = "/navigation")
	public JsonRespResult<NavigationResult> navigationBook(@RequestBody SearchVo searchVo,HttpServletRequest request) {
		NavigationResult tree=new NavigationResult();
		JsonRespResult<NavigationResult> respResult=new JsonRespResult<>();
		try {
			if (StringUtils.isEmpty(searchVo.getKeyword())){
				return respResult.success(tree);
			}else {
				boolean pinyin = PingYinUtil.isPinyin(searchVo.getKeyword());
				if (pinyin){
					return respResult.success(tree);
				}
			}
			TPlatUser user = null;
			try {
				user = securityPlatuserUtils.getUser(request);
			}catch(CustomException ignored) {}
			//保存检索记录
			saveTUserSearchHistoryGeneral(searchVo, request, EsConstants.SEARCH_TYPE_F, user);
			String[] fields= {"resourceClassesId"};
			for (String field:fields){
				List<Navigation> collect = searchService.findAggregationSearch(searchVo,field);
				if (field.equals("resourceClassesId")){
					//分类
					List<Navigation> list = classicTreeService.getSearchTree(collect);
					tree.setResourceClassesList(list);
				}
			}
		} catch (Exception e) {
			log.error("获取书目信息导航出现异常！", e);
			return respResult.error("系统异常请联系管理员");
		}
		return respResult.success(tree);
	}

	@ApiOperation(value = "左侧分类导航-高级检索",notes="接口应答字段参见Swagger Models->左侧分类导航结果应答实体类")
	@PostMapping(value = "/navigation/high")
	public JsonRespResult<NavigationResult> navigationBook(@RequestBody AdvanceSearchVo searchVo,HttpServletRequest request) {
		NavigationResult tree=new NavigationResult();
		JsonRespResult<NavigationResult> respResult=new JsonRespResult<>();
		try {
			List<TSearchVo> searchList = searchVo.getSearchList();
			if (StringUtil.isEmpty(searchList)) {
				return respResult.success(tree);
			}else {
				//判断是否有拼音
				String keys = searchList.stream().filter(f->StringUtil.equalsAny(f.getQueryField(),"mainContent","bookName","author"))
						.map(TSearchVo::getKeyword).collect(Collectors.joining(""));
				boolean pinyin = PingYinUtil.isPinyin(keys);
				if (pinyin){
					return respResult.success(tree);
				}
			}
			TPlatUser user = null;
			try {
				user = securityPlatuserUtils.getUser(request);
			}catch(CustomException ignored) {}
			//保存检索记录
			saveTUserSearchHistoryAdvanced(searchVo, request, EsConstants.SEARCH_TYPE_A, user);
			String[] fields= {"resourceClassesId"};
			for (String field:fields){
				List<Navigation> collect = searchService.findAggregationSearch(searchVo,field);
				if (field.equals("resourceClassesId")){
					//分类
					List<Navigation> list = classicTreeService.getSearchTree(collect);
					tree.setResourceClassesList(list);
				}
			}
		} catch (Exception e) {
			log.error("获取书目信息导航出现异常！", e);
			return respResult.error("系统异常请联系管理员");
		}
		return respResult.success(tree);
	}

	/**
	 * 保存检索记录-普通检索
	 *
	 * @param searchVo
	 * @param request
	 */
	private void saveTUserSearchHistoryGeneral(SearchVo searchVo,HttpServletRequest request, String searchType, TPlatUser user) {

		if (user!=null&&StringUtils.isNotEmpty(searchVo.getKeyword())){
			String searchJson=JSON.toJSONString(searchVo);
			saveTUserSearchHistory(searchVo.getKeyword(), searchType, request, user,searchJson);
		}
	}

	/**
	 * 保存检索记录-高级
	 *
	 * @param request
	 */
	private void saveTUserSearchHistoryAdvanced(AdvanceSearchVo advanceSearchVo, HttpServletRequest request, String searchType, TPlatUser user) {
		if (user!=null){
			if (StringUtil.isNotEmpty(advanceSearchVo.getSearchList())) {
				List<TSearchVo> searchList = advanceSearchVo.getSearchList();
				String searchJson = JSONObject.toJSONString(advanceSearchVo);
				saveTUserSearchHistory(JSONObject.toJSONString(searchList), searchType, request, user,searchJson);
			}
		}
	}

	/**
	 * 保存检索记录
	 *
	 * @param request
	 */
	private void saveTUserSearchHistory(String searchContent,String searchType,
			HttpServletRequest request, TPlatUser user,String searchJson) {
		searchContent=searchContent.trim();
		TUserSearchHistory history = new TUserSearchHistory();
		history.setId(IdUtils.simpleUUID());
		history.setDelFlag(DelFlagEnum.NORMAL.getCode());
		history.setSearchContent(searchContent);
		history.setSearchType(searchType);
		history.setReqIp(IpUtils.getIpAddr(request));
		history.setCreatebyId(user.getId());
		history.setCreatebyName(user.getLoginName());
		history.setOrgId(user.getOrgId());
		history.setSearchContentJson(searchJson);
		searchHistoryService.insertTUserSearchHistory(history);
	}

}