<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="分组名称" prop="groupName">
        <el-select v-model="queryParams.groupName" placeholder="请选择分组" clearable size="small" style="width: 180px">
          <el-option v-for="item in groupOptions" :key="item.dictValue" :label="item.dictLabel"
            :value="item.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small" style="width: 180px">
          <el-option label="上架" value="0" />
          <el-option label="下架" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['tsystem:banner:add']">{{ buttonText.add }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['tsystem:banner:edit']">{{ buttonText.edit }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['tsystem:banner:remove']">{{ buttonText.delete }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['tsystem:banner:export']">{{ buttonText.export }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange" border stripe
      highlight-current-row style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266', height: '20px', padding: '0px' }"
      :cell-style="{ padding: '0px' }" :row-style="{ height: '40px' }" :header-row-style="{ height: '20px' }"
      height="calc(100vh - 340px)">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" min-width="55" align="center" />
      <el-table-column label="分组名称" align="center" prop="groupName" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getGroupTagType(scope.row.groupName)">{{ getGroupName(scope.row.groupName) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="内容介绍" align="center" prop="content" min-width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <div class="ellipsis-content">{{ stripTags(scope.row.content) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="display" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="140">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" :loading="scope.row.loading"
            @change="handleStatusChange(scope.row)" active-text="上架" inactive-text="下架">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createbyName" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="160" />
      <el-table-column label="操作" align="center" width="190px" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['tsystem:banner:edit']">{{ buttonText.edit }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['tsystem:banner:remove']">{{ buttonText.delete }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="dynamicRules" label-width="100px">
        <el-form-item label="分组名称" prop="groupName">
          <el-select v-model="form.groupName" placeholder="请选择分组" style="width: 300px" @change="handleGroupChange">
            <el-option v-for="item in groupOptions" :key="item.dictValue" :label="item.dictLabel"
              :value="item.dictValue" />
          </el-select>
          <span style="margin-left: 10px; color: #909399; font-size: 12px;">{{ getGroupDescription(form.groupName)
            }}</span>
        </el-form-item>

        <el-form-item label="排序" prop="display">
          <el-input-number v-model="form.display" :min="1" :max="999" style="width: 200px" />
        </el-form-item>

        <el-form-item v-if="form.groupName === 'G4'" label="地市" prop="area">
          <el-input v-model="form.area" placeholder="请输入地市" maxlength="50" style="width: 300px" />
        </el-form-item>

        <el-form-item v-if="form.groupName === 'G2' || form.groupName === 'G4'" label="内容介绍" prop="content">
          <div style="border: 1px solid #dcdfe6;">
            <Toolbar style="border-bottom: 1px solid #dcdfe6" :editor="editor" :defaultConfig="toolbarConfig"
              mode="default" />
            <Editor style="height: 300px; overflow-y: hidden;" v-model="editorContent" :defaultConfig="editorConfig"
              mode="default" @onCreated="handleEditorCreated" @onChange="handleEditorChange" />
          </div>
        </el-form-item>

        <el-form-item :label="getUploadLabel()" prop="filePath">
          <div v-if="form.groupName">
            <el-upload 
              v-if="form.groupName === 'G1'" 
              class="avatar-uploader"
              action="#" 
              :http-request="requestUpload"
              :show-file-list="false"
              :before-upload="beforeVideoUpload"
              :on-progress="uploadVideoProcess"
            >
              <video 
                v-if="form.filePath" 
                width="100%" 
                controls="controls" 
                :src="getVideoSrc(form.filePath)" 
                style="max-height: 200px; max-width: 350px;"
              ></video>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <el-progress
                v-if="videoFlag"
                type="line"
                :percentage="videoUploadPercent"
                style="margin-top: 10px"
              ></el-progress>
            </el-upload>

            <div v-if="form.filePath && form.groupName === 'G1'" style="margin-top: 10px; display: flex;">
              <el-button 
                size="small" 
                type="text" 
                icon="el-icon-delete" 
                @click="handleRemoveVideo"
              >删除视频</el-button>
            </div>

            <p v-if="form.groupName === 'G1'" style="color: #606266; font-size: 12px; margin-top: 5px;">
              说明: 视频格式为mp4格式，每个视频大小不超过300MB
            </p>

            <el-upload 
              v-if="form.groupName !== 'G1'"
              v-loading="uploadLoading" 
              ref="upload" 
              action="#" 
              :http-request="requestUpload"
              :before-upload="form.groupName === 'G3' ? beforeImageUpload : beforeImageUpload"
              :on-preview="handlePictureCardPreview" 
              :on-remove="handleRemove" 
              :limit="1" 
              :on-exceed="handleExceed"
              :file-list="fileList" 
              list-type="picture-card"
              :accept="getAcceptType()">
              <i class="el-icon-plus"></i>
            </el-upload>
          </div>

          <div v-else style="text-align: center; padding: 20px; border: 1px dashed #d9d9d9; color: #999;">
            请先选择分组类型
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisible" width="70%" center>
      <img v-if="isImage" width="100%" :src="dialogFileUrl" alt="" />
      <video v-else width="100%" controls :src="dialogFileUrl" style="max-height: 500px;" />
    </el-dialog>
  </div>
</template>

<script>
import {
  listBanner,
  getBanner,
  delBanner,
  addBanner,
  updateBanner,
  exportBanner,
  uploadAvatar,
  changeStatus
} from "@/api/tsystem/banner";
import { debounce } from 'lodash';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "Banner",
  components: { Editor, Toolbar },
  data() {
    return {
      loading: true,
      uploadLoading: false,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      bannerList: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: undefined,
        status: undefined
      },
      form: {
        id: null,
        groupName: null,
        filePath: null,
        fileType: null,
        content: null,
        linkUrl: null,
        display: 1,
        area: null,
        status: "0",
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null
      },
      groupOptions: [],
      dialogFileUrl: "",
      dialogVisible: false,
      fileList: [],
      isImage: true,
      buttonText: {
        confirm: '确定',
        cancel: '取消',
        delete: '删除',
        export: '导出',
        add: '新增',
        edit: '修改'
      },
      editor: null,
      editorContent: '',
      editorConfig: {
        placeholder: '请输入内容介绍...',
        MENU_CONF: {
          uploadImage: {
            server: process.env.VUE_APP_BASE_API + '/common/upload',
            fieldName: 'uploadfile',
            maxFileSize: 5 * 1024 * 1024,
            maxNumberOfFiles: 10,
            allowedFileTypes: ['image/*'],
            customInsert(res, insertFn) {
              if (res.code === 200) {
                insertFn(res.url, '', res.url);
              } else {
                this.$message.error('图片上传失败');
              }
            }
          }
        }
      },
      toolbarConfig: {
        excludeKeys: [
          'uploadVideo',
          'insertTable',
          'codeBlock',
          'todo'
        ]
      },
      videoFlag: false,
      videoUploadPercent: 0
    };
  },
  computed: {
    dynamicRules() {
      const baseRules = {
        groupName: [
          { required: true, message: "请选择分组名称", trigger: "change" }
        ],
        display: [
          { required: true, message: "请输入排序号", trigger: "blur" },
          { type: 'number', message: "排序号必须为数字", trigger: "blur" }
        ]
      };

      if (this.form.groupName === 'G4') {
        baseRules.area = [
          { required: true, message: "请输入地市", trigger: "blur" },
          { min: 2, max: 50, message: "地市长度必须在2到50个字符之间", trigger: "blur" }
        ];
      }

      if (this.form.groupName === 'G2' || this.form.groupName === 'G4') {
        baseRules.content = [
          { required: true, message: "请输入内容介绍", trigger: "blur" },
          { min: 10, message: "内容介绍不能少于10个字符", trigger: "blur" }
        ];
      }

      return baseRules;
    }
  },
  watch: {
    'form.content': {
      handler(val) {
        this.editorContent = val || '';
      },
      immediate: true
    }
  },
  created() {
    this.getList();
    this.getDicts("sys_banner_group").then(response => {
      this.groupOptions = response.data;
    });
  },
  methods: {
    getList() {
      this.loading = true;
      console.log('查询参数:', this.queryParams);
      listBanner(this.queryParams)
        .then((response) => {
          console.log('返回数据:', response);
          this.bannerList = response.rows;
          this.total = response.total;
        })
        .catch(error => {
          console.error('获取数据失败:', error);
          this.msgError("获取数据失败：" + (error.message || "未知错误"));
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getGroupName(groupName) {
      const group = this.groupOptions.find(item => item.dictValue === groupName);
      return group ? group.dictLabel : groupName;
    },
    getGroupTagType(groupName) {
      const typeMap = { 'G1': 'primary', 'G2': 'success', 'G3': 'warning', 'G4': 'info' };
      return typeMap[groupName] || '';
    },
    getGroupDescription(groupName) {
      const group = this.groupOptions.find(item => item.dictValue === groupName);
      return group ? group.remark : '请选择分组';
    },
    getUploadLabel() {
      const labels = { 'G1': '视频:', 'G2': '图片文件:', 'G3': '图片文件:', 'G4': '图片文件:' };
      return labels[this.form.groupName] || '文件上传:';
    },
    handleGroupChange(value) {
      this.fileList = [];
      this.form.filePath = null;
      this.form.content = null;
      this.form.area = null;
    },
    handleContentChange: debounce(function (event) {
      this.form.content = event.target.innerHTML;
    }, 300),
    handleCancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        groupName: null,
        filePath: null,
        fileType: null,
        content: null,
        linkUrl: null,
        display: 1,
        area: null,
        status: "0",
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null
      };
      this.fileList = [];
      this.editorContent = '';
      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    selectable(row, index) {
      return true;
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加轮播图";
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBanner(id).then((response) => {
        this.form = response.data;
        if (this.form.filePath != null) {
          let obj = new Object();
          obj.url = process.env.VUE_APP_BASE_API + this.form.filePath;
          this.fileList.push(obj);
        }
        if (!this.form.filePath) {
          this.fileList = [];
        }
        this.open = true;
        this.title = "修改轮播图";
        this.$nextTick(() => {
          if ((this.form.groupName === 'G2' || this.form.groupName === 'G4') && this.$refs.editor) {
            this.$refs.editor.innerHTML = this.form.content || '';
          }
        });
      });
    },
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) {
          this.msgError("请完善必填信息");
          return;
        }

        if (!this.form.filePath) {
          this.msgError("请上传文件");
          return;
        }

        if ((this.form.groupName === 'G2' || this.form.groupName === 'G4') && !this.form.content) {
          this.msgError("请填写内容介绍");
          return;
        }

        if (this.form.groupName === 'G4' && !this.form.area) {
          this.msgError("请填写地市");
          return;
        }

        try {
          this.loading = true;
          if (this.form.id != null) {
            await updateBanner(this.form);
            this.msgSuccess("修改成功");
          } else {
            await addBanner(this.form);
            this.msgSuccess("新增成功");
          }
          this.open = false;
          this.getList();
        } catch (error) {
          this.msgError("操作失败：" + (error.message || "未知错误"));
        } finally {
          this.loading = false;
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('确认删除选中的数据项?', "警告", {
        confirmButtonText: this.buttonText.confirm,
        cancelButtonText: this.buttonText.cancel,
        type: "warning"
      }).then(async () => {
        await delBanner(ids);
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(error => {
        if (error !== 'cancel') {
          this.msgError("删除失败：" + (error.message || "未知错误"));
        }
      });
    },
    handleExport() {
      this.$confirm("确认导出所有数据?", "警告", {
        confirmButtonText: this.buttonText.confirm,
        cancelButtonText: this.buttonText.cancel,
        type: "warning"
      }).then(async () => {
        const response = await exportBanner(this.queryParams);
        this.download(response.msg);
      }).catch(error => {
        if (error !== 'cancel') {
          this.msgError("导出失败：" + (error.message || "未知错误"));
        }
      });
    },
    requestUpload(val) {
      const formData = new FormData();
      formData.append("uploadfile", val.file);
      
      if (this.form.groupName === 'G1') {
        this.videoFlag = true;
        this.videoUploadPercent = 0;
      } else {
        this.uploadLoading = true;
      }

      uploadAvatar(formData)
        .then((response) => {
          if (response.code === 200) {
            this.form.filePath = response.filePath;
            if (this.form.groupName === 'G1') {
              this.form.fileType = 'MP4';
            } else if (this.form.groupName === 'G3') {
              this.form.fileType = response.fileType || 'IMG';
            } else {
              this.form.fileType = 'JPG';
            }
            this.msgSuccess("上传成功");
          } else {
            this.msgError(response.msg || "上传失败");
          }
        })
        .catch(error => {
          this.msgError("上传失败：" + (error.message || "未知错误"));
        })
        .finally(() => {
          this.uploadLoading = false;
          if (this.form.groupName === 'G1') {
            this.videoFlag = false;
          }
        });
    },
    handleRemove() {
      this.form.filePath = "";
      this.fileList = [];
    },
    handlePictureCardPreview(file) {
      let url = file.url;
      if (url && !/^https?:\/\//.test(url)) {
        url = process.env.VUE_APP_BASE_API + url;
      }
      if (url && url.endsWith('.mp4')) {
        this.dialogFileUrl = url;
        this.isImage = false;
      } else {
        this.dialogFileUrl = url;
        this.isImage = true;
      }
      this.dialogVisible = true;
    },
    beforeVideoUpload(file) {
      const isMP4 = file.type === 'video/mp4';
      const isLt300M = file.size / 1024 / 1024 < 300;

      if (!isMP4) {
        this.msgError('只能上传MP4格式的视频文件！');
        return false;
      }
      if (!isLt300M) {
        this.msgError('视频大小不能超过300MB！');
        return false;
      }
      return true;
    },
    beforeImageUpload(file) {
      const isImage = file.type.indexOf('image/') !== -1;
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        this.msgError('只能上传图片文件！');
        return false;
      }
      // if (!isLt5M) {
      //   this.msgError('上传图片大小不能超过5MB！');
      //   return false;
      // }
      return true;
    },
    beforeGifUpload(file) {
      const isGIF = file.type === 'image/gif';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isGIF) {
        this.msgError('只能上传GIF格式的图片文件！');
        return false;
      }
      // if (!isLt10M) {
      //   this.msgError('上传GIF大小不能超过10MB！');
      //   return false;
      // }
      return true;
    },
    handleExceed() {
      this.msgError("最多上传1个文件");
    },
    async handleStatusChange(row) {
      try {
        row.loading = true;
        await changeStatus(row.id, row.status);
        this.msgSuccess(row.status === "0" ? "上架成功" : "下架成功");
      } catch (error) {
        this.msgError("操作失败：" + (error.message || "未知错误"));
        row.status = row.status === "0" ? "1" : "0";
      } finally {
        row.loading = false;
      }
    },
    handleEditorCreated(editor) {
      this.editor = editor;
    },
    handleEditorChange(editor) {
      this.form.content = editor.getHtml();
    },
    beforeDestroy() {
      if (this.editor) {
        this.editor.destroy();
        this.editor = null;
      }
    },
    stripTags(html) {
      if (!html) return '';
      return html.replace(/<[^>]+>/g, '').replace(/\n/g, ' ');
    },
    previewVideo() {
      if (this.form.filePath) {
        this.dialogFileUrl = process.env.VUE_APP_BASE_API + this.form.filePath;
        this.isImage = false;
        this.dialogVisible = true;
      }
    },
    getAcceptType() {
      return this.form.groupName === 'G1' ? 'video/mp4' : 'image/*';
    },
    getVideoSrc(filePath) {
      if (!filePath) return '';
      if (filePath.startsWith('http')) return filePath;
      return process.env.VUE_APP_BASE_API + filePath;
    },
    handleRemoveVideo() {
      this.form.filePath = null;
      this.fileList = [];
      this.$message.success("视频已删除，可重新上传");
    },
    uploadVideoProcess(event) {
      this.videoFlag = true;
      this.videoUploadPercent = Math.round((event.loaded / event.total) * 100);
    }
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
}

.mb8 {
  margin-bottom: 8px;
}

.el-tag {
  margin-right: 5px;
}

.ellipsis-content {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.w-e-text-container {
  height: 300px !important;
}

.w-e-toolbar {
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
}

::v-deep .upload-file .el-upload--picture-card {
  width: 148px;
  height: 200px;
  line-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .upload-file .el-upload-list--picture-card .el-upload-list__item {
  width: 148px;
  height: 200px;
}

.el-table {
  margin-top: 30px;
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa !important;
  color: #606266;
  font-weight: 500;
  height: 20px;
  padding: 0;
}

.el-table td {
  padding: 0;
}

.el-button--text+.el-button--text {
  margin-left: 8px;
}

.dialog-footer {
  margin-top: -30px;
}
</style>

<style lang="scss" scoped>
::v-deep .el-upload-list__item {
  transition: none !important;
}

.avatar-uploader {
  display: inline-block;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 350px;

  &:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 350px;
    height: 200px;
    line-height: 200px;
    text-align: center;
  }
}
</style>
